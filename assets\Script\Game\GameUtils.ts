// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import AudioManager from "../Base/Components/AudioManager";
import LayerManger from "../Base/Components/LayerManger";
import CameraControl from "../CameraControl";
import Enemy from "../GameStuffAndComps/Enemy";
import EnemyCreator from "./EnemyCreator";
import GameDirector from "./GameDirector";
import GameStuffManager from "./GameStuffManager";
import GameStuffs from "./GameStuffs";
import GameWorld from "./GameWorld";
import GameWorldUI from "./GameWorldUI";
import GatheringBuilding from "../GameStuffAndComps/GatheringBuilding";
import GuideToStatue from "./GuideToStatue";
import Hero from "../GameStuffAndComps/Hero";
import HeroControl from "./HeroControl";
import NPC from "../GameStuffAndComps/NPC";
import NPCCreator from "./NPCCreator";
import PathPoints from "./PathPoints";
import { ResourceType } from "../GameStuffAndComps/Resource";
import StatueUnlockManager from "./StatueUnlockManager";
import StorageArea from "../GameStuffAndComps/StorageArea";
import Unit from "../GameStuffAndComps/Unit";
import GameConfigs from "../GameConfigs";
import GameManager from "../GameManager";
import JoystickArea from "../JoystickArea";
import Layer_1 from "../Layer_1";
import LocalUtils, { StringDictionary } from "../LocalUtils";
import MyPhysicsExtension from "../MyPhysics/MyPhysicsExtension";
import SmithyUtils from "../Smithy/SmithyUtils";
import PointDirectionPanel from "../UI/PointDirectionPanel";
import UserDataManager from "../UserDataManager";

// if(CC_DEBUG) {
window['GM'] = {
    cc: cc,

    LocalUtils: LocalUtils,

    logGameInfo: (ref = 0)=>{
        GameManager.instance.scheduleOnce(()=>{
            if(ref == 0) {
                let allNodeCount = LocalUtils.AllChildrenCountOfNode(GameManager.instance.node);
                console.log(`------ now running callback num : ${GameManager.instance.NumOfRunningCallback}, All Node Count: ${allNodeCount}`);
                GameManager.instance.LogCostTime();
            } else if(ref == 1) {
                console.log(`------ bullet num : ${LocalUtils.AllChildrenCountOfNode(GameManager.instance.node, 'bullet', true)}`);
            } else if(ref == 2) {
                console.log(`------ enemy num : ${LocalUtils.AllChildrenCountOfNode(GameManager.instance.node, 'enemy', true)}`);
            } else if(ref == 3) {
                console.log(`------ coin num : ${LocalUtils.AllChildrenCountOfNode(GameManager.instance.node, 'coin', true)}`);
            } else if(ref == 4) {
                console.log(`------ all node : `, LocalUtils.FindAllChildrenOfNode(GameManager.instance.node));
            } else if(ref == 5) {
                let callbacks = GameManager.instance.RunningCallbacks;
                let callbackMarks: string[] = [];
                callbacks.forEach((e)=>{
                    callbackMarks.push(e.mark);
                });
                console.log(`------ callbackMarks : `, callbackMarks);
            }
        }, 0.1);
        return 'Game: 2073_ArmForge';
    },

    enemyCanMove: (ref = 0)=>{
        if(ref == 0) {
            GameUtils.isCanEnemyMove = true;
        } else if(ref == 1) {
            GameUtils.isCanEnemyMove = false;
        }
    },

    killAllEnemy: ()=>{
        GameManager.instance.scheduleOnce(()=>{
            GameUtils.enemyList.forEach((e)=>{
                e.script.GetHurt(GameUtils.mainHero.script, 1000);
            });
        }, 0.1);
    },

    gameDirector: ()=>{ return GameDirector.instance; },
    gameConfigs: ()=>{ return GameConfigs.instance; },

    LogDrawCallInfomation: ()=>{
        // @ts-ignore
        let models = cc.renderer._handle._batchedModels;
        let logString = '';
        logString += `length: ${models.length}\n`;
        models.forEach((model, i)=>{
            let string = '';
            let effect = model._effect._effect._name;
            let node = model._node;
            let name = node._name;

            if(i % 20 == 0) {
                string += '\n';
            }
            string += '@' + effect.slice(0, 3) + '=';
            if(name != 'New Node') {
                string += name.slice(0, 3) + '_';
            } else {
                string += '!!__';
            }
            logString += string;
        });

        console.log(logString);
        // cc.game.canvas.getContext('webgl').getExtension('WEBGL_debug_renderer_info');
    },
}
// }

// 开局关闭 BGM
window['notAutoPlayBgm'] = true;


const {ccclass, property} = cc._decorator;

@ccclass
export default class GameUtils {

    public static rootUINode: cc.Node = null;
    public static rootJoystickArea: JoystickArea = null;

    public static rootLayer_1: Layer_1 = null;
    public static rootCameraControl: CameraControl = null;
    public static rootGameWorld: GameWorld = null;
    public static rootGameStuffs: GameStuffs = null;
    public static rootGameWorldUI: GameWorldUI = null;
    public static rootGuideToStatue: GuideToStatue = null;
    public static rootHeroControl: HeroControl = null;
    public static rootEnemyCreator: EnemyCreator = null;
    public static rootNPCCreator: NPCCreator = null;
    public static rootPathPoints: PathPoints = null;
    public static rootPointDirectionPanel: PointDirectionPanel = null;
    
    public static get mainHeroBackpack(): StorageArea {
        // return (this.mainHero.script as Hero).heroBackpackStorageArea;
        return (this.mainHero.script as Hero).backpack.backpackStorageArea;
    }
    // public static mainHero: Hero;
    public static get mainHero(): UnitInfo { return GameStuffManager.instance.mainHero; }
    public static get mainBoss(): UnitInfo { return GameStuffManager.instance.mainBoss; }
    public static get heroList(): UnitInfo[] { return GameStuffManager.instance.heroList; }
    public static get enemyList(): UnitInfo[] { return GameStuffManager.instance.enemyList; }
    public static get npcLoggerList(): UnitInfo[] { return GameStuffManager.instance.npcLoggerList; }
    public static get npcBuyerList(): UnitInfo[] { return GameStuffManager.instance.npcBuyerList; }
    public static get soldierList(): NPC[] { return GameStuffManager.instance.soldierList; }
    public static heroNum = 1;

    public static gameWorldRectSize: cc.Size;

    public static coinNum = 0;

    public static isCanEnemyMove = true;

    public static isFirstTouch = false;

    public static isDebugAntiExtrusion = false;

    private static _isAllowCameraShaking: boolean[] = [];
    private static _isCameraShaking = false;


    public static GameStart() {
        // cc.Canvas.instance.node.on('touchstart', ()=>{
        //     console.log('canvas touch start!');
        // }, true)
        GameManager.instance.LateFrameCall(()=>{
            // // @ts-ignore
            // cc.director.__calculateDeltaTime = cc.director.calculateDeltaTime
            // // @ts-ignore
            // cc.director.calculateDeltaTime = function (...args) {
            //     this.__calculateDeltaTime(...args)
            //     this._deltaTime *= GameUtils.isFirstTouch ? 1 : 0.3
            // }
            GameManager.instance.LateFrameCall(()=>{
                this.rootJoystickArea.ShowGuideFinger();
                // cc.director.pause();
                GameManager.instance.GamePause();
                cc.Canvas.instance.node.on('touchstart', ()=>{
                    // console.log('canvas touch start!');
                    if(!this.isFirstTouch && UserDataManager.instance.isPanelClosed) {
                        this.isFirstTouch = true;
                        // cc.director.resume();
                        GameManager.instance.GameResume();
                    }
                }, true);
            });
        });

        this.rootHeroControl.Init();
        // this.rootLayer_1.RefreshCoinNumUI(this.coinNum);
        GameStuffManager.instance.Init();
        StatueUnlockManager.instance.InitStatueUnlockUI();
        StatueUnlockManager.instance.InitFloorUnlockAreaUI();

        GameManager.instance.LateTimeCall(()=>{
            let allNodeCount = LocalUtils.AllChildrenCountOfNode(GameManager.instance.node);
            let callbacks = GameManager.instance.RunningCallbacks;
            let callbackMarks: string[] = [];
            callbacks.forEach((e)=>{
                callbackMarks.push(e.mark);
            });
            console.log(`------ now running callback num : ${GameManager.instance.NumOfRunningCallback}, All Node Count: ${allNodeCount}`);
            // console.log(`------ bullet num : ${LocalUtils.AllChildrenCountOfNode(GameManager.instance.node, 'bullet', true)}`);
            // console.log(`------ enemy num : ${LocalUtils.AllChildrenCountOfNode(GameManager.instance.node, 'enemy', true)}`);
            // console.log(`------ coin num : ${LocalUtils.AllChildrenCountOfNode(GameManager.instance.node, 'coin', true)}`);
            // console.log(`------ all node : `, LocalUtils.FindAllChildrenOfNode(GameManager.instance.node));
            // console.log(`------ callbackMarks : `, callbackMarks);
            // GameManager.instance.LogCostTime();

            // if(GameDirector.instance.guideStep == 1) {
            //     GameDirector.instance.GuideToGatheringBuilding();
            // }
            // if(GameDirector.instance.guideStep == 2) {
            //     GameDirector.instance.GuideToStockade();
            // }
        }, 5);

        this.rootLayer_1.OnSwitchGamePause(false);

        this.rootJoystickArea.ShowGuideFinger();

        // GameUtils.rootEnemyCreator.isCreatingBoss = true;
        // GameUtils.rootEnemyCreator.AllowCreateBoss();

        GameDirector.instance.GameStart();
    }

    // public static OpenSmithyPanel() {
    //     SmithyUtils.SmithyPanelOpenLevel += 1;
    //     this.rootLayer_1.OpenSmithyPanel();
    // }

    public static OnCloseSmithPanel() {
        GameManager.instance.GameResume();
    }

    public static OpenHeroChoosePanel(generatePos: cc.Vec2) {
        GameManager.instance.GamePause();
        this.rootLayer_1.OpenHeroChoosePanelNormal(generatePos);
    }

    public static OpenHeroChoosePanelOnTower(towerIndex: number) {
        GameManager.instance.GamePause();
        this.rootLayer_1.OpenHeroChoosePanelOnTower(towerIndex);
    }

    public static OnCloseHeroChoosePanel(unchooseBoardIndex: number, chooseBoardIndex: number) {
        this.rootLayer_1.OnCloseHeroChoosePanel(unchooseBoardIndex, chooseBoardIndex);
        GameManager.instance.GameResume();
    }

    public static OnHeroGetHurt() {
        this.rootLayer_1.PlayRedBorder();
    }

    public static CheckPosIsOnCollision(pos: cc.Vec2) {
        let worldPos = GameUtils.rootGameWorld.MiddleNode.convertToWorldSpaceAR(pos);
        let rect = cc.rect(worldPos.x, worldPos.y, 80, 60);
        let result = MyPhysicsExtension.Instance.MyTestAABB(rect);
        if(result.length <= 0) {
            // console.log(`区域内检测到碰撞体！`);
            return false;
        }
        return true;
    }

    public static GetResourceNum(type: ResourceType) {
        return GameUtils.mainHeroBackpack.GetNumOfResource().resourceNums[type];
    }

    public static GetSpDieMarkByBulletRef(bulletRef: number) {
        if(bulletRef == 30) {
            return 1;
        } else if(bulletRef == 3 || bulletRef == 4 || bulletRef == 15) {
            return 2;
        } else if(bulletRef == 2) {
            return 4;
        }
        return 0;
    }
    
    public static GetResourceTypeByID(id: number) {
        let type = ResourceType.none;
        if(id == 1) {
            type = ResourceType.wood;
        } else if(id == 2) {
            type = ResourceType.meat;
        } else if(id == 3) {
            type = ResourceType.coin;
        } else if(id == 4) {
            type = ResourceType.bow;
        } else if(id == 5) {
            type = ResourceType.wood2;
        } else if(id == 6) {
            type = ResourceType.rawMeat;
        } else if(id == 7) {
            type = ResourceType.food;
        } else if(id == 8) {
            type = ResourceType.groundMeat;
        } else if(id == 9) {
            type = ResourceType.sword;
        } else if(id == 10) {
            type = ResourceType.blockClod;
        } else if(id == 11) {
            type = ResourceType.blockWood;
        } else if(id == 12) {
            type = ResourceType.blockGold;
        }
        return type;
    }

    public static GetResourceIDByType(resourceType: ResourceType) {
        let id = 0;
        if(resourceType == ResourceType.wood) {
            id = 1;
        } else if(resourceType == ResourceType.meat) {
            id = 2;
        } else if(resourceType == ResourceType.coin) {
            id = 3;
        } else if(resourceType == ResourceType.bow) {
            id = 4;
        } else if(resourceType == ResourceType.wood2) {
            id = 5;
        } else if(resourceType == ResourceType.rawMeat) {
            id = 6;
        } else if(resourceType == ResourceType.food) {
            id = 7;
        } else if(resourceType == ResourceType.groundMeat) {
            id = 8;
        } else if(resourceType == ResourceType.sword) {
            id = 9;
        } else if(resourceType == ResourceType.blockClod) {
            id = 10;
        } else if(resourceType == ResourceType.blockWood) {
            id = 11;
        } else if(resourceType == ResourceType.blockGold) {
            id = 12;
        }
        return id;
    }

    public static GetDirByTowerId(tower_id: number) {
        let dir = cc.v2(1, 0);
        dir = LocalUtils.AngleToVec2(50);
        if(tower_id == 3) {
            dir = LocalUtils.AngleToVec2(-160);
        } else if(tower_id == 4) {
            dir = LocalUtils.AngleToVec2(-70);
        } else if(tower_id == 5) {
            dir = LocalUtils.AngleToVec2(90);
        } else if(tower_id == 6) {
            dir = LocalUtils.AngleToVec2(50);
        }
        // if(tower_id == 2) {
        //     // 上方
        //     dir = cc.v2(0, 1).normalize();
        // } else if(tower_id == 3) {
        //     // 左方
        //     dir = cc.v2(-1, -0.3).normalize();
        // } else if(tower_id == 4) {
        //     // 下方中间（原右上）
        //     // dir = cc.v2(1, 0.8).normalize();
        //     dir = cc.v2(-0.7, -1).normalize();
        // } else if(tower_id == 5) {
        //     // 下方
        //     dir = cc.v2(0.2, -1).normalize();
        // }
        return dir;
    }

    public static GetDirRangeByTowerId(tower_id: number) {
        let range = 360;
        if(tower_id == 3) {
            range = 200;
        } else if(tower_id == 4) {
            range = 200;
        } else if(tower_id == 4) {
            range = 200;
        } else if(tower_id == 4) {
            range = 200;
        } else {
            range = 200;
        }
        // if(tower_id == 2) {
        //     range = 200;
        // } else if(tower_id == 3) {
        //     range = 230;
        // } else if(tower_id == 4) {
        //     range = 170;
        // } else if(tower_id == 5) {
        //     range = 230;
        // }
        return range;
    }

    public static AddCoin(value: number) {
        this.coinNum += value;
        this.rootLayer_1.RefreshCoinNumUI(this.coinNum);
    }

    public static ReduceCoin(value: number) {
        this.coinNum -= value;
        this.rootLayer_1.RefreshCoinNumUI(this.coinNum);
    }

    public static CoinAdd(value: number) {
        console.log(`coin add! +${value}`);
        
        // this.AddCoin(10);
        this.mainHeroBackpack.CreateResources(ResourceType.coin, value);
        // this.mainHeroBackpack.CreateResources(ResourceType.wood2, value);
    }

    public static WeaponAttackSpeedUp(value: number) {
        console.log(`weapon attck speed up! +${value}%`);
        
        (this.mainHero.script as Hero).WeaponAttackSpeedUp(value);
    }

    public static WeaponBulletCopyUp(value: number) {
        console.log(`weapon bullet copy up! +${value}`);
        
        (this.mainHero.script as Hero).WeaponBulletCopyUp(value);
    }

    public static HeroAdd() {
        console.log(`Hero Add! +1  now == ${this.heroNum}`);
        
        let generatePos = this.mainHero.script.rootPosition.clone();
        GameUtils.OpenHeroChoosePanel(generatePos);
        // this.rootNPCCreator.CreateANPCSoldier();
    }

    public static NPCAdd(value: number) {
        console.log(`NPC Add! +${value}  now == ${this.npcLoggerList.length}`);
        
        this.rootNPCCreator.CreateANPCLogger();
        this.rootNPCCreator.CreateANPCMeatCollector();
        this.rootNPCCreator.CreateANPCArcher();
        // this.rootNPCCreator.CreateANPCSoldier();
    }

    public static NPC2Add(value: number) {
        console.log(`NPC2 Add! +${value}  now == ${this.npcBuyerList.length}`);
        
        this.rootNPCCreator.CreateANPCBuyer();
        // this.rootNPCCreator.CreateANPCSoldier();
    }

    public static EnemyAdd(value: number) {
        console.log(`Enemy Add! +${value}  now == ${this.enemyList.length + 1}`);
        
        this.rootEnemyCreator.CreateAEnemy();
    }

    public static BossAdd(value: number) {
        console.log(`Boss Add! +${value}`);
        
        this.rootEnemyCreator.CreateABoss();
    }

    public static RefreshGameWorldRectSize() {
        let viewSize = cc.view.getVisibleSize();
        console.log(`viewSize: ${viewSize}`);
        let cameraZoomRatio = this.rootCameraControl.camera.zoomRatio;
        this.gameWorldRectSize = cc.size(viewSize.width / cameraZoomRatio, viewSize.height / cameraZoomRatio);
        console.log(`gameWorldRectSize: ${this.gameWorldRectSize}`);
    }

    // 缩小 0.8 倍，对应增大 1.25 倍
    public static Fake3dDistance(pos1: cc.Vec2, pos2: cc.Vec2) {
        return cc.v2(pos1.x, pos1.y * 0.8).sub(cc.v2(pos2.x, pos2.y * 0.8)).len();
    }
    public static Fake3dDistanceExpand(pos1: cc.Vec2, pos2: cc.Vec2) {
        return cc.v2(pos1.x, pos1.y * 1.25).sub(cc.v2(pos2.x, pos2.y * 1.25)).len();
    }

    public static Fake3dSpeed(speed: number, dir: cc.Vec2) {
        let nDir = dir.normalize();
        // let a = Math.sqrt(1 - (1 - (0.8 * 0.8)) * nDir.y);
        let a = Math.sqrt(1 - Math.abs(nDir.y) * 0.36);
        return speed = speed * a;
    }

    public static CameraShakeAddTime(shakerTime: number) {
        // console.log(`camera shake add time! ${shakerTime}s`);
        let isAllowShakingListIndex = this._isAllowCameraShaking.length;
        this._isAllowCameraShaking[isAllowShakingListIndex] = true;
        this._isCameraShaking = true;
        let leftShakerTime = shakerTime;
        let loopTimes = 0;
        while(loopTimes < 100 && leftShakerTime > 0) {
            loopTimes += 1;
            let useTime = Math.random() * 0.2 + 0.2;
            if(leftShakerTime - useTime < 0) {
                useTime = leftShakerTime;
            }
            GameManager.instance.LateTimeCallOnce(()=>{
                if(!this._isAllowCameraShaking[isAllowShakingListIndex]) return;
                this.rootCameraControl.CameraShake(useTime);
            }, shakerTime - leftShakerTime);
            leftShakerTime -= useTime;
            // if(leftShakerTime > 0) {
            //     this.CameraShakeAddTime(leftShakerTime);
            // }
        }
        GameManager.instance.LateTimeCallOnce(()=>{
            this.CameraShakeStop(isAllowShakingListIndex);
        }, shakerTime);
        return isAllowShakingListIndex;
    }

    public static CameraShakeStop(isAllowShakingListIndex: number = -1) {
        if(isAllowShakingListIndex == -1) return;
        this._isAllowCameraShaking[isAllowShakingListIndex] = false;
        let isShaking = false;
        this._isAllowCameraShaking.forEach((e)=>{
            if(!isShaking && e) {
                isShaking = true;
            }
        });
        this._isCameraShaking = isShaking;
    }

    public static CameraShake(shakeTime = 0.3) {
        this.rootCameraControl.CameraShake(shakeTime);
    }

    // public static PushWithoutRepeat<T>(arr: Array<T>, element: T) {
    //     let findResult = arr.find((e)=>{
    //         return e == element;
    //     });
    //     if(!findResult) {
    //         arr.push(element);
    //     }
    // }

    // public static RayCastGroup(points: cc.Vec2[]) {
    //     let allResults: cc.PhysicsCollider[] = [];
    //     let pointGroups = [];
    //     let physicsManager = cc.director.getPhysicsManager();
    //     if(points.length >= 2) {
    //         points.forEach((e, index)=>{
    //             if(index < points.length - 1) {
    //                 pointGroups[index + 1] = [];
    //                 pointGroups[index + 1][1] = e;
    //                 if(index == 0) {
    //                     pointGroups[0] = [];
    //                     pointGroups[0][0] = e;
    //                 } else {
    //                     pointGroups[index][0] = e;
    //                 }
    //             } else {
    //                 pointGroups[index][0] = e;
    //                 pointGroups[0][1] = e;
    //             }
    //         })
    //         pointGroups.forEach((e)=>{
    //             let startPos = e[0];
    //             let endPos = e[0];
    //             let results = physicsManager.rayCast(startPos, endPos, cc.RayCastType.All);
    //             results.forEach((result)=>{
    //                 this.PushWithoutRepeat(allResults, result.collider);
    //             });
    //         });
    //     }
    //     // console.log(`result: ${allResults.length}`, allResults);
    //     points.forEach((e, index)=>{
    //         let collider = physicsManager.testPoint(e);
    //         if(collider) {
    //             this.PushWithoutRepeat(allResults, collider);
    //         }
    //     })
        
    //     return allResults;
    // }
}


window['GM'].GameUtils = GameUtils;


export class UnitInfo {
    node: cc.Node;
    script: Unit;
    
    constructor(node: cc.Node) {
        this.node = node;
    }
}

export class ObjectPool<T> {
    private _objectList: T[] = [];

    private _onTakeCallback: Function = (obj: T)=>{};
    private _onPutCallback: Function = (obj: T)=>{};

    constructor(callback?: Function) {

    }

    SetOnTakeCallback(callback: Function) {
        this._onTakeCallback = callback;
    }

    SetOnPutCallback(callback: Function) {
        this._onPutCallback = callback;
    }

    Size() {
        let size = 0;
        this._objectList.forEach((e)=>{
            size += 1;
        });
        return size;
    }

    Take(): T {
        let obj = null;
        if(this.Size() > 0) {
            obj = this._objectList.pop();
            this._onTakeCallback(obj);
        }
        return obj;
    }

    Put(obj: T) {
        this._objectList.push(obj);
        this._onPutCallback(obj);
    }
}

export class DataCountBucket {
    private _dataCountList: number[] = [];
    private _tagIndexList: StringDictionary<number>[] = [];
    private _tags: string[] = [];

    toString() {
        let str = '';
        this._tags.forEach((e)=>{
            str = str.concat(e);
            str = str.concat(`: ${this.GetDataCount(e)}\n`);
        });
        return str;
    }

    putIn(tag: string, num = 1) {
        this.SetDataCount(tag, this.GetDataCount(tag) + num);
    }

    tackOut(tag: string, num = 1) {
        let count = this.GetDataCount(tag);
        if(count - num >= 0) {
            this.SetDataCount(tag, count - num);
        } else {
            this.SetDataCount(tag, 0);
        }
    }

    clear(tag: string) {
        this.SetDataCount(tag, 0);
    }

    clearAll() {
        this._tags = [];
        this._tagIndexList = [];
        this._dataCountList = [];
    }

    protected SetDataCount(tag: string, count: number) {
        this._dataCountList[this.GetTagIndex(tag)] = count;
    }

    protected GetDataCount(tag: string) {
        let dataCount = this._dataCountList[this.GetTagIndex(tag)];
        if(dataCount) {
            let count: number = dataCount;
            return count;
        } else {
            return 0;
        }
    }

    protected GetTagIndex(tag: string) {
        let index = this._tagIndexList[tag];
        if(!index) {
            index = this._tags.length;
            this._tagIndexList[tag] = index;
            this._dataCountList[index] = 0;
            this._tags.push(tag);
        }
        return index;
    }
}
