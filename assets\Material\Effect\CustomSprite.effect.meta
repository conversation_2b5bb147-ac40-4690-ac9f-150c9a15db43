{"ver": "1.0.25", "uuid": "1f64dded-55a7-44ca-b28b-bf3273c0d79a", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\n#if USE_TINT\nattribute vec4 a_color0;\n#endif\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nvarying vec3 v_modelPos;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  #if CC_USE_MODEL\n  v_modelPos = a_position;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nstruct hsv { float h; float s; float v; };\nvec3 fromHSV (hsv hsvInput);\nhsv toHsv(vec3 color);\nfloat ArrayMaxLen3(float array[3]);\nfloat ArrayMinLen3(float array[3]);\nvarying vec4 v_color;\nvarying vec3 v_modelPos;\nuniform vec4 mixColor;\nuniform float mixRatio;\nuniform float hyalinize;\nuniform float hyalinizeHeight;\n#if USE_TEXTURE\nvarying vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nvoid main () {\n  float ratio = mixRatio;\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture2D(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  float ax = 1.0;\n  #if CC_USE_MODEL\n  float y = v_modelPos.y;\n  if(hyalinize >= 1.0) {\n    ax = 0.0;\n  } else if(hyalinizeHeight > 0.0) {\n    if(y < hyalinizeHeight) {\n      ax = 1.0 - hyalinize;\n    } else {\n    }\n  }\n  #endif\n  float a = o.a * ax;\n  o = vec4(o.rgb * (1.0 - ratio) + mixColor.rgb * ratio, a);\n  ALPHA_TEST(o);\n  gl_FragColor = o;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\n#if USE_TINT\nin vec4 a_color0;\n#endif\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nout vec3 v_modelPos;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  #if CC_USE_MODEL\n  v_modelPos = a_position;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nstruct hsv { float h; float s; float v; };\nvec3 fromHSV (hsv hsvInput);\nhsv toHsv(vec3 color);\nfloat ArrayMaxLen3(float array[3]);\nfloat ArrayMinLen3(float array[3]);\nin vec4 v_color;\nin vec3 v_modelPos;\nuniform mix {\n  vec4 mixColor;\n  float mixRatio;\n};\nuniform HSV {\n  int H;\n  int S;\n  int V;\n};\nuniform hyalinize {\n  float hyalinize;\n  float hyalinizeHeight;\n};\n#if USE_TEXTURE\nin vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nvoid main () {\n  float ratio = mixRatio;\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  float ax = 1.0;\n  #if CC_USE_MODEL\n  float y = v_modelPos.y;\n  if(hyalinize >= 1.0) {\n    ax = 0.0;\n  } else if(hyalinizeHeight > 0.0) {\n    if(y < hyalinizeHeight) {\n      ax = 1.0 - hyalinize;\n    } else {\n    }\n  }\n  #endif\n  float a = o.a * ax;\n  o = vec4(o.rgb * (1.0 - ratio) + mixColor.rgb * ratio, a);\n  ALPHA_TEST(o);\n  gl_FragColor = o;\n}"}}], "subMetas": {}}