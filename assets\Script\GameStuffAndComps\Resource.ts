// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import LocalUtils, { OutParabolicEquation, TweenObject } from "../LocalUtils";
import Stuff from "./Stuff";
import Unit from "./Unit";
import GameUtils from "../Game/GameUtils";
import ResourceGroup from "./ResourceGroup";
import GameStuffManager from "../Game/GameStuffManager";

const {ccclass, property} = cc._decorator;

@ccclass
export default class Resource extends Stuff {

    // 可飞行到目标资源组中

    @property(cc.Integer)
    resource_id: number = 0;

    @property(cc.Sprite)
    img: cc.Sprite = null;
    // @property(cc.Sprite)
    // img2: cc.Sprite = null;

    info: ResourceInfo = null;

    rootNode: cc.Node = null;
    viewNode: cc.Node = null;
    shadowNode: cc.Node = null;

    static resourceMoveSpeed = 155;

    standTowerId: number = -1;
    pathPointsPos: cc.Vec2[] = [];
    pathPointIndex = 0;

    isOnGroundCanPush = false;

    private _flyTarget: Stuff = null;
    private _flyTargetPos: cc.Vec2 = null;
    private _isFlying = false;
    private _isFlyCompleteTweenPlaying = false;
    private _isFlyCompleteTweenPlayed = false;
    private _isFlyComplete = false;

    private _flyTime = 0.6;
    private _flyLeftTime = 0.6;
    private _flySrcPos = cc.v2();
    private _flySrcHeight = 0;
    private _parabolicEquationData: OutParabolicEquation;
    
    private _isMoveToPathPoint = false;
    private _pathPointRef = 0;
    private _movingPathPointPos: cc.Vec2 = cc.v2();
    private _isArrivePathEnd = false;

    private _lastRootPosition: cc.Vec2 = null;

    private _onArrivePathEndCallback: Function;
    private _onArriveTargetAreaCallback: Function;

    private _onDestroyCallback: Function = ()=>{};
    private _onCompliteCallback: Function = ()=>{};
    private _onArriveCallback: Function = ()=>{};

    protected override InitOnLoad() {
        super.InitOnLoad();
        this.rootNode = this.node.getChildByName('node');
        this.viewNode = this.rootNode.getChildByName('view');
        this.shadowNode = this.rootNode.getChildByName('shadow');

        this.gameUpdateCallbackId = GameManager.instance.AddGameUpdate('Resource', (dt: number)=>{
            this.gameUpdate(dt);
        }, false, 2110);

        this.viewNode.y = this.height;
    }

    start(): void {
        // this.DisplayLabel();
    }

    gameUpdate(dt: number) {
        if(!this.isRunning) {
            return;
        }
        this.info.lifeTime += dt;
        if(this.info.lifeTime > 1) {
            this.info.isCanPickUp = true;
        }
        // this.DisplayLabel();
        if(this.info.isInGroup) {
            if(!this.info.resourceGroup) {
                // console.error('no resourceGroup !');
            } else {
                this.rootPosition = this.info.resourceGroup.rootPosition;
                this.shadowNode.y = this.info.resourceGroup.ResourceBaseYAdd;
            }
            this.viewNode.y = this.height;
            if(this.info.sortIndex == 0) {
                this.shadowNode.active = true;
            } else {
                this.shadowNode.active = false;
            }
            // this.viewNode.opacity = 255;
            // if(this.info.resourceGroup.resourceGroup_id == 10) {
                // this.img.node.color = cc.color(200, 0, 0);
            // } else if(this.info.resourceGroup.resourceGroup_id == 11) {
            //     this.img.node.color = cc.color(10, 150, 0);
            // } else if(!this.info.resourceGroup) {
            //     this.img.node.color = cc.color(10, 0, 88);
            // }
        } else {
            if(this._flyTargetPos) {
                this.shadowNode.active = false;
            } else {
                this.shadowNode.active = true;
            }
            if(this.info.isAlive) {
                if(this._isMoveToPathPoint) {
                    if(!this._isArrivePathEnd) {
                        this.MoveToTargetPos(dt, this._movingPathPointPos);
                        let distance = this._movingPathPointPos.sub(this.rootPosition).len();
                        if(distance < 10) {
                            this._isMoveToPathPoint = false;
                            this.ArrivePathPoint();
                        } else {
                            // console.log(`distance: ${distance}!`);
                        }
                    }
                }
                if(!this.info.isNoSort) {
                    // this.img.node.color = cc.color(10, 0, 88);
                    // this.viewNode.y = this.height;
                } else {
                    // this.img.node.color = cc.color(255, 255, 255);
                }
                // this.viewNode.opacity = 120;
                // this.viewNode.scale = 1.5;
            }
        }
        // if(!this.info.isAlive) { this.shadowNode.active = false; }
        if(this._isFlying) {
            this._flyLeftTime -= dt;
            if(this._flyTarget) {
                this.MoveToTargetByTime(dt, this._flyLeftTime, this._flyTarget.rootPosition);
            } else if(this._flyTargetPos){
                this.MoveToTargetByTime(dt, this._flyLeftTime, this._flyTargetPos);
            }
        }
        if(this.isOnGroundCanPush) {
            this.SetRootPosition(cc.v2(this.node.position));
            let newPos = this.rootPosition;
            let areaTrigerCollider = GameUtils.rootGameWorld.gameStuffs.trigger_area_groundMeat;
            let areaPolygon: cc.Vec2[] = [];
            areaTrigerCollider.points.forEach((e, i)=>{
                areaPolygon[i] = GameUtils.rootGameWorld.MiddleNode.convertToNodeSpaceAR(areaTrigerCollider.node.convertToWorldSpaceAR(e));
            });
            let isInArea = cc.Intersection.pointInPolygon(newPos, areaPolygon);
            if(!isInArea) {
                let inAreaNewPos = LocalUtils.ClosestPointToPolygon(newPos, areaPolygon);
                newPos = inAreaNewPos;
            }
            if(this._lastRootPosition) {
                let moveDir = newPos.sub(this._lastRootPosition);
                let moveDirAngle = LocalUtils.Vec2ToAngle(moveDir);
                if(moveDirAngle <= 180 && moveDirAngle > 30 || moveDirAngle >= -180 && moveDirAngle < -150) {
                    newPos = LocalUtils.ClosestPointToLine(newPos, this._lastRootPosition,
                        this._lastRootPosition.add(LocalUtils.AngleToVec2(30)));
                }
            }
            this.rootPosition = newPos;
            this._lastRootPosition = this.rootPosition.clone();

            let targetStorageArea = GameUtils.rootGameWorld.meatStorageArea;
            let distance = GameUtils.Fake3dDistanceExpand(this.rootPosition, targetStorageArea.rootPosition);
            if(distance < 250) {
                // console.log(`资源到达终点！`);
                this.isOnGroundCanPush = false;
                this._onArriveTargetAreaCallback();
            }
        }
    }
    // SetHeight(height: number) {
    //     this.height = height;
    // }

    ReFresh() {
        // console.log(`resource [${this.info.resourceGroup.resourceGroup_id}] ReFresh()! parent: ${this.node.parent.parent.parent.name}`);
        this.viewNode.y = this.height;
    }

    SetImg(ref: number) {
        // if(ref == 1) {
        //     this.img.node.active = true;
        //     this.img2.node.active = false;
        // } else if(ref == 2) {
        //     this.img.node.active = false;
        //     this.img2.node.active = true;
        // }

    }
    
    MoveToTargetByTime(dt: number, leftTime: number, pos: cc.Vec2) {
        let viewNode = this.viewNode;
        // let shadow = this.node.getChildByName('shadow');
        let moveDir = pos.sub(this._flySrcPos);
        let distance = 0
        let progress = leftTime / this._flyTime;
        if(progress < 0) {
            progress = 0;
        }
        if(leftTime > 0) {
            distance = moveDir.len();
        }
        if(progress <= 0) {
            this.rootPosition = pos;
            if(!this._isFlyCompleteTweenPlayed && !this._isFlyCompleteTweenPlaying) {
                this._isFlyCompleteTweenPlaying = true;
                if(this.info.type == ResourceType.coin) {
                    LocalUtils.PlaySingleSound('coin');
                } else if(this.info.type == ResourceType.meat || this.info.type == ResourceType.rawMeat || this.info.type == ResourceType.food) {
                    // if(this.info.resourceGroup.storageAreaOner.isHeroBackpack) {
                    //     LocalUtils.PlaySingleSound('item_move');
                    // } else {
                        LocalUtils.PlaySingleSound('meat');
                    // }
                } else if(this.info.type == ResourceType.wood || this.info.type == ResourceType.wood2) {
                    LocalUtils.PlaySingleSound('wood');
                } else {
                    LocalUtils.PlaySingleSound('item_move');
                }
                this._onArriveCallback();
                this.ReFresh();
                // GameManager.instance.scheduleOnce(()=>{
                cc.tween(this.viewNode).to(0.08, {scale: 1.25}).to(0.06, {scale: 1}).call(()=>{
                    this._isFlyCompleteTweenPlayed = true;
                    this._isFlyCompleteTweenPlaying = false;
                }).start();
                // }, 0.2);
            }

            if(this._isFlyCompleteTweenPlayed && !this._isFlyComplete) {
                this._isFlyComplete = true;
                // LocalUtils.PlaySingleSound('collect', false, 0.5);
                // console.log('###');
                this._onCompliteCallback();
                // if(shadow) {
                //     shadow.opacity = 0;
                // }
                // GameUtils.rootGameWorldUI.PlayCollect();
                // cc.tween(viewNode).to(0.2, {scale: 0.1, opacity: 0}).delay(0.1).call(()=>{
                //     this.node.destroy();
                // }).start();
                this._isFlying = false;
                this._flyTarget = null;
                this._flyTargetPos = null;
            }
        } else {
            this.rootPosition = this._flySrcPos.add(cc.v2(moveDir.mul(1 - progress)));
            let data = this._parabolicEquationData;
            viewNode.y = data.a * (1 - progress) * (1 - progress) + data.b * (1 - progress) + data.c;
        }
        // console.log(`meat [${this.node.uuid}]: progress: ${progress}, y: ${viewNode.y}, this.rootPosition.x: ${this.rootPosition.x}`);
        
    }

    MoveToTargetPos(dt: number, pos: cc.Vec2) {
        let moveDir = pos.sub(this.rootPosition);
        let distance = moveDir.len();
        let moveDistance = Resource.resourceMoveSpeed * dt;
        let newPos = cc.v2();
        if(moveDistance >= distance) {
            if(distance > 0.1) {
                newPos = pos;
            } else {
            }
        } else {
            newPos = this.rootPosition.add(moveDir.normalize().mul(moveDistance));
        }
        this.rootPosition = newPos;

        // console.log(`target pos: ${pos}`);
        // console.log(`moveDir: ${moveDir.normalize()}this.rootPosition: ${this.rootPosition}`);
        
    }

    DropAndJumpToPos(pos: cc.Vec2, movePosOffset: cc.Vec2, isPlayDropAnim: boolean) {
        let anim = this.rootNode.getComponent(cc.Animation);
        if(anim && isPlayDropAnim) {
            if(this.info.type == ResourceType.meat) {
                anim.play('resource_meat_drop');
            } else if(this.info.type == ResourceType.wood) {
                anim.play('resource_wood_drop');
            } else if(this.info.type == ResourceType.coin) {
                anim.play('resource_coin_drop');
            } else if(this.info.type == ResourceType.wood2) {
                anim.play('resource_wood_drop');
            } else if(this.info.type == ResourceType.rawMeat) {
                anim.play('resource_meat_drop');
            } else if(this.info.type == ResourceType.groundMeat) {
                anim.play('resource_meat_drop');
            }
        }
        if(!movePosOffset.equals(cc.v2())) {
            cc.tween(new TweenObject<number>(0, (value: number)=>{
                this.rootPosition = pos.add(movePosOffset.mul(value));
            })).set({value: 0}).to(0.4, {value: 0.8}).to(0.25, {value: 1}, {easing: 'quadOut'}).start();
        }
    }
    

    FlyToTargetGroup(targetResourceGroup: ResourceGroup, srcPos: cc.Vec2, srcHeight: number) {
        // console.log(`flyToTargetGroup() srcPos: ${srcPos}`);
        if(!this._isFlying) {
            this.FlyToTarget(srcPos, srcHeight, true, targetResourceGroup);
        }
    }

    FlyToTargetPos(targetPos: cc.Vec2, srcPos: cc.Vec2, srcHeight: number) {
        if(!this._isFlying) {
            this.FlyToTarget(srcPos, srcHeight, false, null, targetPos);
        }
    }

    FlyToTarget(srcPos: cc.Vec2, srcHeight: number, isTargetIsGroup: boolean, targetResourceGroup?: ResourceGroup, targetPos?: cc.Vec2) {
        this._isFlying = true;
        this._isFlyCompleteTweenPlaying = false;
        this._isFlyCompleteTweenPlayed = false;
        this._isFlyComplete = false;
        this._flySrcPos = srcPos;
        this._flySrcHeight = srcHeight;

        LocalUtils.PlaySingleSound('item_fly', false, 0.3);
        let out: OutParabolicEquation = {a: 0, b: 0, c: 0};
        let distance = 0;
        if(isTargetIsGroup) {
            this._flyTarget = targetResourceGroup;
            distance = this._flyTarget.rootPosition.sub(this._flySrcPos).len();
        } else {
            this._flyTargetPos = targetPos;
            distance = this._flyTargetPos.sub(this._flySrcPos).len();
        }
        let heightUp = 50;
        if(distance <= 600) {
            heightUp = 50 + distance / 600 * 150;
        } else {
            heightUp = 200;
        }
        let topHeight = this._flySrcHeight > this.height ? (this._flySrcHeight + heightUp) : (this.height + heightUp);
        let heightDiff = Math.abs(topHeight - (this._flySrcHeight < this.height ? this._flySrcHeight : this.height));
        this._flyTime = this.GetFlyTime(distance, heightDiff);
        this._flyLeftTime = this._flyTime;

        LocalUtils.ParabolicEquationByYTop(out, 0, 1, topHeight, this._flySrcHeight, this.height);
        this._parabolicEquationData = out;
        
        // LocalUtils.ChangeParentWithoutMoving(this.node, GameUtils.rootGameWorldUI.effParent);
        this.node.parent = GameUtils.rootGameWorld.frontStuffParent;
        if(this.info.type == ResourceType.food) {
            this.node.parent = GameUtils.rootGameWorld.floatStuffParent;
        }
        // this.node.parent = GameUtils.rootGameWorld.MiddleNode;
    }

    MoveOnConveyer(conveyerIndex: number) {
        this.info.state = ResourceState.autoMoving;
        this.info.resourceGroup = null;
        this.info.isNoSort = true;
        this.info.script.height = 0;
        this.info.script.ReFresh();
        this.node.parent = GameUtils.rootGameWorld.MiddleNode;

        this.pathPointsPos = GameUtils.rootPathPoints.GetConveyerPathPointsPos(conveyerIndex);
        this._pathPointRef = 1;
        this._isMoveToPathPoint = true;
        this.ArrivePathPoint();
    }

    Flip(isLeft: boolean) {
        this.rootNode.scaleX = isLeft ? -1 : 1;
    }

    ShowHideOutlight(isShow: boolean) {
        let outlight = this.img.node.getChildByName('outlight');
        if(outlight) {
            outlight.active = isShow;
        }
    }
    
    SetOnArrivePathEndCallback(callback: Function) {
        this._onArrivePathEndCallback = callback;
    }

    ArrivePathPoint() {
        let endOfPathPointRef = this.pathPointsPos.length;
        if(this._pathPointRef <= endOfPathPointRef) {
            this.GoToNextPathPoint();
        } else {
            // console.log(`资源到达终点？`);
        }
    }

    GoToNextPathPoint() {
        let endOfPathPointRef = this.pathPointsPos.length;
        this._pathPointRef += 1;
        if(this._pathPointRef <= endOfPathPointRef) {
            this._movingPathPointPos = this.GetPathPointPosition(this._pathPointRef - 1);
            this._isMoveToPathPoint = true;
        } else {
            // console.log(`资源到达终点！`);
            this._isArrivePathEnd = true;
            this._onArrivePathEndCallback && this._onArrivePathEndCallback();
            // this.DestroySelf();
        }
    }

    GetPathPointPosition(pathPointIndex: number) {
        return this.pathPointsPos[pathPointIndex];
    }

    MoveToGroundCanPush(moveTime: number = 0) {
        if(moveTime <= 0) {
            this.isOnGroundCanPush = true;
            this.OpenCollider();
        } else {
            this.CloseCollider();
            GameManager.instance.LateTimeCallOnce(()=>{
                this.isOnGroundCanPush = true;
                this.OpenCollider();
            }, moveTime);
        }
        this.SetOnArriveTargetAreaCallback(()=>{
            this.CloseCollider();
            GameUtils.rootGameWorld.meatStorageArea.FindAGroup(false, this.info.type).PutInNoSrc(this.info);
            GameStuffManager.instance.RemoveResourceFromList(this.info);
        });
    }

    SetOnArriveTargetAreaCallback(callback: Function) {
        this._onArriveTargetAreaCallback = callback;
    }

    CloseCollider() {
        let collider = this.node.getComponent(cc.PhysicsCollider);
        if(collider) {
            collider.enabled = false;
        }
    }

    OpenCollider() {
        let collider = this.node.getComponent(cc.PhysicsCollider);
        if(collider) {
            collider.enabled = true;
        }
    }

    SetOnArriveCallback(callback: Function) {
        this._onArriveCallback = callback;
    }

    SetOnCompliteCallback(callback: Function) {
        this._onCompliteCallback = callback;
    }

    SetOnDestroyCallback(callback: Function) {
        this._onDestroyCallback = callback;
    }

    GetFlyTime(distance: number, heightDiff: number) {
        let baseFlyTime = 0.3;
        let flyTime = 0;
        let moveDistancePow = (distance / 10 + heightDiff * heightDiff * 0.6);
        if(moveDistancePow <= 200000) {
            flyTime = 0.1 + moveDistancePow / 200000 * (baseFlyTime - 0.1);
        } else {
            flyTime = baseFlyTime;
        }
        return flyTime;
    }
    
    protected override SetPos(pos: cc.Vec2): cc.Vec2 {
        let node = this.node;
        node.x = pos.x; 
        node.y = pos.y;
        // GameUtils.rootGameWorld.TryReSortSelf(this);
        // GameUtils.rootGameWorld.ReSortStuffs();
        return cc.v2(node.x, node.y);
    }

    protected override onDestroy(): void {
        this._onDestroyCallback();
        super.onDestroy();
    }

    DisplayLabel() {
        let lab = this.node.getChildByName('node').getChildByName('view').getChildByName('lab').getComponent(cc.Label);
        lab.node.active = true;
        if(this.info.resourceGroup) {
            // lab.string = '[' + this.info.sortIndex + ']';
            lab.string = (this.info.isHeightOutLimit ? ' #' : '-') + ' [' + this.info.sortIndex + '=' + Math.floor(this.height) + ']';
            // lab.string = '[' + Math.floor(this.rootPosition.x) + '=' + Math.floor(this.rootPosition.y) + ']';
            // lab.string = '[' + this.node.parent.parent.parent.name + ']';
        } else {
            lab.string = '[]';
        }
    }
}


export class ResourceInfo {
    node: cc.Node;
    script: Resource;
    type: ResourceType = ResourceType.none;
    state: ResourceState = ResourceState.none;
    isNoEntity = false;
    isNoSort = false;
    get isInGroup() {
        return this.state == ResourceState.inGroup;
        // return this.state == ResourceState.flyingToGroup || this.state == ResourceState.flyingToGroupArrived;
    }
    get isAlive() {
        return this.state != ResourceState.readyToDestroy && this.state != ResourceState.isGone;
    }
    get isInList() {
        return this.resourceGroup && this.resourceGroup.resourceList.includes(this); 
    }
    get isOnGroud() {
        return this.state == ResourceState.onGround;
    }
    resourceGroup: ResourceGroup = null;
    sortIndex: number = 0;
    height: number = 0;
    isHeightOutLimit = false;
    lifeTime = 0;
    isCanPickUp = false;
    
    constructor(node: cc.Node) {
        this.node = node;
    }

    InitData() {
        this.lifeTime = 0;
    }
}

export enum ResourceState {
    none,
    onGround,
    inGroup,
    autoMoving,
    flyingToGroup,
    flyingToGroupArrived,
    flyingToPos,
    readyToDestroy,
    isGone,
}

export enum ResourceType {
    none,
    wood,
    meat,
    coin,
    bow,
    wood2,
    rawMeat,
    food,
    groundMeat,
    sword,
    blockClod,
    blockWood,
    blockGold,
}

export type ResourceNumType = {
    [type in ResourceType]: number;
};

export class ResourceNumList {
    // resourceNums: ResourceNumType = [0, 0, 0, 0, 0, 0];
    resourceNums: ResourceNumType = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

    constructor() {
        // this.resourceNums[ResourceType.money] = 0;
        // this.resourceNums[ResourceType.meat1] = 0;
        // this.resourceNums[ResourceType.meat2] = 0;
        // this.resourceNums[ResourceType.food1] = 0;
        // this.resourceNums[ResourceType.food2] = 0;
        this.resourceNums[ResourceType.wood] = 0;
        this.resourceNums[ResourceType.meat] = 0;
        this.resourceNums[ResourceType.coin] = 0;
        this.resourceNums[ResourceType.bow] = 0;
        this.resourceNums[ResourceType.wood2] = 0;
        this.resourceNums[ResourceType.rawMeat] = 0;
        this.resourceNums[ResourceType.food] = 0;
        this.resourceNums[ResourceType.groundMeat] = 0;
        this.resourceNums[ResourceType.sword] = 0;
        this.resourceNums[ResourceType.blockClod] = 0;
        this.resourceNums[ResourceType.blockWood] = 0;
        this.resourceNums[ResourceType.blockGold] = 0;
    }
}