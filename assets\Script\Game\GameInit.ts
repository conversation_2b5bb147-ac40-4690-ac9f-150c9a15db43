import { MyAssembler } from "../MyAssembler";


let gameInit = {
    x: ()=> {
        // console.log('init!');
    },
};

cc.game.once(cc.game.EVENT_GAME_INITED, (...args)=>{
    console.log('-------EVENT_GAME_INITED-------');
    // console.log(args);
    if(CC_DEBUG) {
        console.log(cc);
        gameInit.x();
    }
});

export class ClassFactory {
    _inited = false;

    _Init() {
        console.log('ClassFactory Init!');

    }

    get MyAssembler(): typeof MyAssembler {
        if(this._inited) {
            return MyAssembler;
        } else {
            return null;
        }
    };

    public static CreateInstance<T>(type: {new(): T}): T {
        return new type();
    }
}