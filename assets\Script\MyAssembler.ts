

// export declare class MyAssembler extends cc.Assembler2D {
//     updateRenderData (sprite): void;
//     updateUVs (sprite): void;
//     updateVerts (sprite): void;
//     fillBuffers (sprite, renderer): void;
// }

export function MyAssembler(): typeof cc.Assembler2D {
    // const Assembler2D = cc.Assembler2D;
    return class extends cc.Assembler2D {
        
        floatsPerVert = 6;
        verticesCount = 4;
        indicesCount = 6;
        uvOffset = 2;
        colorOffset = 4;

        // texCoordOffset = 8;

        initData() {
            let data = this._renderData;
            // createFlexData支持创建指定格式的renderData
            data.createFlexData(0, this.verticesCount, this.indicesCount, this.getVfmt());

            // createFlexData不会填充顶点索引信息，手动补充一下
            let indices = data.iDatas[0];
            let count = indices.length / 6;
            for (let i = 0, idx = 0; i < count; i++) {
                let vertextID = i * 4;
                indices[idx++] = vertextID;
                indices[idx++] = vertextID+1;
                indices[idx++] = vertextID+2;
                indices[idx++] = vertextID+1;
                indices[idx++] = vertextID+3;
                indices[idx++] = vertextID+2;
            }
        }

        // 重载getBuffer(), 返回一个能容纳自定义顶点数据的buffer
        // 默认fillBuffers()方法中会调用到
        getBuffer() {
            return cc.renderer._handle.getBuffer("mesh", this.getVfmt());
        }

        // pos数据没有变化，不用重载
        // updateVerts(sprite) {
        // }

        updateUVs(sprite) {
            // uv0调用基类方法写入
            super.updateUVs(sprite);
            // 填入自己的uv1数据
            // ...
            // 方法类似uv0写入，详见Demo
            // https://github.com/caogtaa/CCBatchingTricks
        }

        updateRenderData (sprite) {
            this.packToDynamicAtlas(sprite, sprite._spriteFrame);

            if (sprite._vertsDirty) {
                this.updateUVs(sprite);
                this.updateVerts(sprite);
                sprite._vertsDirty = false;
            }
        }

        updateUVs (sprite) {
            let uv = sprite._spriteFrame.uv;
            let uvOffset = this.uvOffset;
            let floatsPerVert = this.floatsPerVert;
            let verts = this._renderData.vDatas[0];
            for (let i = 0; i < 4; i++) {
                let srcOffset = i * 2;
                let dstOffset = floatsPerVert * i + uvOffset;
                verts[dstOffset] = uv[srcOffset];
                verts[dstOffset + 1] = uv[srcOffset + 1];
            }
        }

        updateVerts (sprite) {
            let node = sprite.node,
                cw = node.width, ch = node.height,
                appx = node.anchorX * cw, appy = node.anchorY * ch,
                l, b, r, t;
            if (sprite.trim) {
                l = -appx;
                b = -appy;
                r = cw - appx;
                t = ch - appy;
            }
            else {
                let frame = sprite.spriteFrame,
                    ow = frame._originalSize.width, oh = frame._originalSize.height,
                    rw = frame._rect.width, rh = frame._rect.height,
                    offset = frame._offset,
                    scaleX = cw / ow, scaleY = ch / oh;
                let trimLeft = offset.x + (ow - rw) / 2;
                let trimRight = offset.x - (ow - rw) / 2;
                let trimBottom = offset.y + (oh - rh) / 2;
                let trimTop = offset.y - (oh - rh) / 2;
                l = trimLeft * scaleX - appx;
                b = trimBottom * scaleY - appy;
                r = cw + trimRight * scaleX - appx;
                t = ch + trimTop * scaleY - appy;
            }

            let local = this._local;
            local[0] = l;
            local[1] = b;
            local[2] = r;
            local[3] = t;
            this.updateWorldVerts(sprite);
        }
    
    }
}

// export default class MyAssembler;

// export class MyAssembler extends cc.Assembler2D {
//     updateRenderData (sprite) {
//         this.packToDynamicAtlas(sprite, sprite._spriteFrame);

//         if (sprite._vertsDirty) {
//             this.updateUVs(sprite);
//             this.updateVerts(sprite);
//             sprite._vertsDirty = false;
//         }
//     }

//     updateUVs (sprite) {
//         let uv = sprite._spriteFrame.uv;
//         let uvOffset = this.uvOffset;
//         let floatsPerVert = this.floatsPerVert;
//         let verts = this._renderData.vDatas[0];
//         for (let i = 0; i < 4; i++) {
//             let srcOffset = i * 2;
//             let dstOffset = floatsPerVert * i + uvOffset;
//             verts[dstOffset] = uv[srcOffset];
//             verts[dstOffset + 1] = uv[srcOffset + 1];
//         }
//     }

//     updateVerts (sprite) {
//         let node = sprite.node,
//             cw = node.width, ch = node.height,
//             appx = node.anchorX * cw, appy = node.anchorY * ch,
//             l, b, r, t;
//         if (sprite.trim) {
//             l = -appx;
//             b = -appy;
//             r = cw - appx;
//             t = ch - appy;
//         }
//         else {
//             let frame = sprite.spriteFrame,
//                 ow = frame._originalSize.width, oh = frame._originalSize.height,
//                 rw = frame._rect.width, rh = frame._rect.height,
//                 offset = frame._offset,
//                 scaleX = cw / ow, scaleY = ch / oh;
//             let trimLeft = offset.x + (ow - rw) / 2;
//             let trimRight = offset.x - (ow - rw) / 2;
//             let trimBottom = offset.y + (oh - rh) / 2;
//             let trimTop = offset.y - (oh - rh) / 2;
//             l = trimLeft * scaleX - appx;
//             b = trimBottom * scaleY - appy;
//             r = cw + trimRight * scaleX - appx;
//             t = ch + trimTop * scaleY - appy;
//         }

//         let local = this._local;
//         local[0] = l;
//         local[1] = b;
//         local[2] = r;
//         local[3] = t;
//         this.updateWorldVerts(sprite);
//     }
// }